import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Настройка стиля графиков
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

def load_and_combine_data():
    """Загружает и объединяет все CSV файлы с данными о заказах"""
    print("📊 Загрузка данных...")

    # Список файлов для загрузки
    files = [
        'orders_info_ru_01-2025.csv',
        'orders_info_ru_02-2025.csv',
        'orders_info_ru_03-2025.csv',
        'orders_info_ru_04-2025.csv',
        'orders_info_ru_05-2025.csv'
    ]

    dataframes = []
    for file in files:
        try:
            df = pd.read_csv(file)
            df['month'] = file.split('_')[-1].split('-')[0]  # Извлекаем месяц из имени файла
            dataframes.append(df)
            print(f"✅ Загружен {file}: {len(df)} записей")
        except Exception as e:
            print(f"❌ Ошибка загрузки {file}: {e}")

    # Объединяем все данные
    combined_df = pd.concat(dataframes, ignore_index=True)
    print(f"\n🎯 Общий объем данных: {len(combined_df)} записей")
    return combined_df

def basic_data_info(df):
    """Выводит базовую информацию о данных"""
    print("\n" + "="*60)
    print("📋 БАЗОВАЯ ИНФОРМАЦИЯ О ДАННЫХ")
    print("="*60)

    print(f"Размер датасета: {df.shape[0]} строк, {df.shape[1]} столбцов")
    print(f"Период данных: {df['month'].min()}-{df['month'].max()} месяцы 2025 года")

    print("\n📊 Типы данных:")
    print(df.dtypes.value_counts())

    print("\n🔍 Пропущенные значения:")
    missing_data = df.isnull().sum()
    missing_percent = (missing_data / len(df)) * 100
    missing_df = pd.DataFrame({
        'Пропуски': missing_data,
        'Процент': missing_percent
    }).sort_values('Пропуски', ascending=False)
    print(missing_df[missing_df['Пропуски'] > 0])

    return missing_df

def analyze_numerical_columns(df):
    """Анализирует числовые столбцы"""
    print("\n" + "="*60)
    print("🔢 АНАЛИЗ ЧИСЛОВЫХ ДАННЫХ")
    print("="*60)

    # Определяем числовые столбцы
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()

    print("Числовые столбцы:", numeric_cols)

    # Базовая статистика
    print("\n📈 Описательная статистика:")
    stats_df = df[numeric_cols].describe()
    print(stats_df.round(2))

    return numeric_cols, stats_df

def detect_outliers_iqr(df, column):
    """Обнаруживает выбросы методом IQR"""
    Q1 = df[column].quantile(0.25)
    Q3 = df[column].quantile(0.75)
    IQR = Q3 - Q1

    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR

    outliers = df[(df[column] < lower_bound) | (df[column] > upper_bound)]

    return outliers, lower_bound, upper_bound

def detect_outliers_zscore(df, column, threshold=3):
    """Обнаруживает выбросы методом Z-score"""
    z_scores = np.abs(stats.zscore(df[column].dropna()))
    outliers = df[z_scores > threshold]

    return outliers, threshold

def analyze_outliers(df, numeric_cols):
    """Анализирует выбросы в числовых данных"""
    print("\n" + "="*60)
    print("🎯 АНАЛИЗ ВЫБРОСОВ")
    print("="*60)

    outlier_summary = {}

    # Анализируем ключевые столбцы
    key_columns = ['sum', 'views_count', 'seller_rating', 'buyer_rating', 'success_fee']
    key_columns = [col for col in key_columns if col in numeric_cols]

    for col in key_columns:
        print(f"\n🔍 Анализ выбросов в столбце '{col}':")

        # Метод IQR
        outliers_iqr, lower_iqr, upper_iqr = detect_outliers_iqr(df, col)

        # Метод Z-score
        outliers_zscore, threshold = detect_outliers_zscore(df, col)

        print(f"  📊 IQR метод: {len(outliers_iqr)} выбросов ({len(outliers_iqr)/len(df)*100:.2f}%)")
        print(f"     Границы: [{lower_iqr:.2f}, {upper_iqr:.2f}]")
        print(f"  📊 Z-score метод: {len(outliers_zscore)} выбросов ({len(outliers_zscore)/len(df)*100:.2f}%)")

        outlier_summary[col] = {
            'iqr_outliers': len(outliers_iqr),
            'iqr_percent': len(outliers_iqr)/len(df)*100,
            'zscore_outliers': len(outliers_zscore),
            'zscore_percent': len(outliers_zscore)/len(df)*100,
            'lower_bound': lower_iqr,
            'upper_bound': upper_iqr
        }

    return outlier_summary

def visualize_data_and_outliers(df, numeric_cols):
    """Создает визуализации данных и выбросов"""
    print("\n" + "="*60)
    print("📊 СОЗДАНИЕ ВИЗУАЛИЗАЦИЙ")
    print("="*60)

    # Ключевые столбцы для визуализации
    key_columns = ['sum', 'views_count', 'seller_rating', 'buyer_rating']
    key_columns = [col for col in key_columns if col in numeric_cols]

    # 1. Распределения основных переменных
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Распределения ключевых переменных', fontsize=16, fontweight='bold')

    for i, col in enumerate(key_columns[:4]):
        row, col_idx = i // 2, i % 2
        ax = axes[row, col_idx]

        # Гистограмма
        df[col].hist(bins=50, alpha=0.7, ax=ax)
        ax.set_title(f'Распределение {col}')
        ax.set_xlabel(col)
        ax.set_ylabel('Частота')
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('distributions.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. Box plots для выявления выбросов
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Box plots - выявление выбросов', fontsize=16, fontweight='bold')

    for i, col in enumerate(key_columns[:4]):
        row, col_idx = i // 2, i % 2
        ax = axes[row, col_idx]

        df.boxplot(column=col, ax=ax)
        ax.set_title(f'Box plot: {col}')
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('boxplots.png', dpi=300, bbox_inches='tight')
    plt.show()

def clean_outliers(df, method='iqr', columns=None):
    """Очищает данные от выбросов"""
    print("\n" + "="*60)
    print("🧹 ОЧИСТКА ДАННЫХ ОТ ВЫБРОСОВ")
    print("="*60)

    if columns is None:
        columns = ['sum', 'views_count', 'seller_rating', 'buyer_rating']
        columns = [col for col in columns if col in df.columns]

    df_cleaned = df.copy()
    removed_count = 0

    for col in columns:
        print(f"\n🔧 Очистка столбца '{col}'...")

        if method == 'iqr':
            outliers, lower_bound, upper_bound = detect_outliers_iqr(df_cleaned, col)

            # Удаляем выбросы
            initial_count = len(df_cleaned)
            df_cleaned = df_cleaned[(df_cleaned[col] >= lower_bound) & (df_cleaned[col] <= upper_bound)]
            removed = initial_count - len(df_cleaned)

            print(f"  📊 Удалено {removed} записей ({removed/initial_count*100:.2f}%)")
            print(f"  📊 Границы: [{lower_bound:.2f}, {upper_bound:.2f}]")

        elif method == 'zscore':
            z_scores = np.abs(stats.zscore(df_cleaned[col].dropna()))
            initial_count = len(df_cleaned)
            df_cleaned = df_cleaned[z_scores <= 3]
            removed = initial_count - len(df_cleaned)

            print(f"  📊 Удалено {removed} записей ({removed/initial_count*100:.2f}%)")

        removed_count += removed

    print(f"\n✅ Общий результат очистки:")
    print(f"   Исходное количество записей: {len(df)}")
    print(f"   Количество записей после очистки: {len(df_cleaned)}")
    print(f"   Удалено записей: {len(df) - len(df_cleaned)} ({(len(df) - len(df_cleaned))/len(df)*100:.2f}%)")

    return df_cleaned

def compare_before_after(df_original, df_cleaned):
    """Сравнивает данные до и после очистки"""
    print("\n" + "="*60)
    print("📊 СРАВНЕНИЕ ДО И ПОСЛЕ ОЧИСТКИ")
    print("="*60)

    key_columns = ['sum', 'views_count', 'seller_rating', 'buyer_rating']
    key_columns = [col for col in key_columns if col in df_original.columns]

    comparison_data = []

    for col in key_columns:
        original_stats = df_original[col].describe()
        cleaned_stats = df_cleaned[col].describe()

        comparison_data.append({
            'Столбец': col,
            'Среднее (до)': original_stats['mean'],
            'Среднее (после)': cleaned_stats['mean'],
            'Медиана (до)': original_stats['50%'],
            'Медиана (после)': cleaned_stats['50%'],
            'Стд.откл (до)': original_stats['std'],
            'Стд.откл (после)': cleaned_stats['std'],
            'Макс (до)': original_stats['max'],
            'Макс (после)': cleaned_stats['max']
        })

    comparison_df = pd.DataFrame(comparison_data)
    print("\n📈 Сравнительная таблица:")
    print(comparison_df.round(2))

    # Визуализация сравнения
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Сравнение распределений до и после очистки', fontsize=16, fontweight='bold')

    for i, col in enumerate(key_columns[:4]):
        row, col_idx = i // 2, i % 2
        ax = axes[row, col_idx]

        # Гистограммы до и после
        df_original[col].hist(bins=50, alpha=0.5, label='До очистки', ax=ax)
        df_cleaned[col].hist(bins=50, alpha=0.5, label='После очистки', ax=ax)

        ax.set_title(f'Сравнение: {col}')
        ax.set_xlabel(col)
        ax.set_ylabel('Частота')
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    return comparison_df

def analyze_categorical_data(df):
    """Анализирует категориальные данные"""
    print("\n" + "="*60)
    print("📊 АНАЛИЗ КАТЕГОРИАЛЬНЫХ ДАННЫХ")
    print("="*60)

    categorical_cols = ['order_cancelled', 'auction_winning', 'refunded', 'is_created_by_api', 'month']

    for col in categorical_cols:
        if col in df.columns:
            print(f"\n🔍 Распределение '{col}':")
            value_counts = df[col].value_counts()
            percentages = df[col].value_counts(normalize=True) * 100

            for value, count in value_counts.items():
                print(f"  {value}: {count} ({percentages[value]:.1f}%)")

def filter_expensive_orders(df, price_threshold=600000):
    """Фильтрует заказы дороже указанной суммы"""
    print("\n" + "="*60)
    print(f"💰 ФИЛЬТРАЦИЯ ДОРОГИХ ЗАКАЗОВ (>{price_threshold:,})")
    print("="*60)

    # Фильтруем заказы дороже порога
    expensive_orders = df[df['sum'] > price_threshold].copy()

    print(f"📊 Найдено дорогих заказов: {len(expensive_orders)} из {len(df)} ({len(expensive_orders)/len(df)*100:.2f}%)")

    if len(expensive_orders) > 0:
        print(f"\n📈 Статистика дорогих заказов:")
        print(f"  Минимальная сумма: {expensive_orders['sum'].min():,}")
        print(f"  Максимальная сумма: {expensive_orders['sum'].max():,}")
        print(f"  Средняя сумма: {expensive_orders['sum'].mean():,.2f}")
        print(f"  Медианная сумма: {expensive_orders['sum'].median():,}")

        # Топ-10 самых дорогих заказов
        print(f"\n🏆 ТОП-10 САМЫХ ДОРОГИХ ЗАКАЗОВ:")
        top_expensive = expensive_orders.nlargest(10, 'sum')[['id', 'sum', 'order_date', 'category_id', 'region_id']]
        for idx, row in top_expensive.iterrows():
            print(f"  {row['sum']:,} руб. - ID: {row['id']} (дата: {row['order_date']}, категория: {row['category_id']})")

        # Анализ по категориям
        print(f"\n📂 РАСПРЕДЕЛЕНИЕ ПО КАТЕГОРИЯМ:")
        category_stats = expensive_orders.groupby('category_id').agg({
            'sum': ['count', 'mean', 'max'],
            'id': 'count'
        }).round(2)
        category_stats.columns = ['Количество', 'Средняя_сумма', 'Макс_сумма', 'Заказов']
        category_stats = category_stats.sort_values('Количество', ascending=False)
        print(category_stats.head(10))

        # Анализ по регионам
        print(f"\n🌍 РАСПРЕДЕЛЕНИЕ ПО РЕГИОНАМ:")
        region_stats = expensive_orders.groupby('region_id').agg({
            'sum': ['count', 'mean', 'max'],
            'id': 'count'
        }).round(2)
        region_stats.columns = ['Количество', 'Средняя_сумма', 'Макс_сумма', 'Заказов']
        region_stats = region_stats.sort_values('Количество', ascending=False)
        print(region_stats.head(10))

    return expensive_orders

def save_cleaned_data(df_cleaned):
    """Сохраняет очищенные данные"""
    print("\n" + "="*60)
    print("💾 СОХРАНЕНИЕ ОЧИЩЕННЫХ ДАННЫХ")
    print("="*60)

    filename = 'orders_cleaned.csv'
    df_cleaned.to_csv(filename, index=False)
    print(f"✅ Очищенные данные сохранены в файл: {filename}")
    print(f"📊 Размер файла: {len(df_cleaned)} записей")

def save_expensive_orders(df_expensive):
    """Сохраняет дорогие заказы"""
    if len(df_expensive) > 0:
        filename = 'expensive_orders.csv'
        df_expensive.to_csv(filename, index=False)
        print(f"✅ Дорогие заказы сохранены в файл: {filename}")
        print(f"📊 Размер файла: {len(df_expensive)} записей")
    else:
        print("❌ Нет дорогих заказов для сохранения")

def main():
    """Основная функция для выполнения полного EDA"""
    print("🚀 ЗАПУСК ИССЛЕДОВАТЕЛЬСКОГО АНАЛИЗА ДАННЫХ (EDA)")
    print("="*60)

    try:
        # 1. Загрузка данных
        df = load_and_combine_data()

        # 2. Базовая информация
        missing_info = basic_data_info(df)

        # 3. Анализ числовых данных
        numeric_cols, stats_df = analyze_numerical_columns(df)

        # 4. Анализ категориальных данных
        analyze_categorical_data(df)

        # 5. Анализ выбросов
        outlier_summary = analyze_outliers(df, numeric_cols)

        # 6. Визуализация
        visualize_data_and_outliers(df, numeric_cols)

        # 7. Очистка данных (метод IQR)
        df_cleaned = clean_outliers(df, method='iqr')

        # 8. Сравнение до и после
        comparison_df = compare_before_after(df, df_cleaned)

        # 9. Сохранение очищенных данных
        save_cleaned_data(df_cleaned)

        print("\n🎉 АНАЛИЗ ЗАВЕРШЕН УСПЕШНО!")
        print("="*60)
        print("📋 Созданные файлы:")
        print("  - distributions.png (распределения переменных)")
        print("  - boxplots.png (box plots для выявления выбросов)")
        print("  - comparison.png (сравнение до и после очистки)")
        print("  - orders_cleaned.csv (очищенные данные)")

        return df, df_cleaned, outlier_summary, comparison_df

    except Exception as e:
        print(f"❌ Ошибка при выполнении анализа: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

if __name__ == "__main__":
    # Запуск анализа
    original_data, cleaned_data, outliers_info, comparison_results = main()