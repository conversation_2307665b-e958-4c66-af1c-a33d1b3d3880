import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_and_combine_data():
    """Загружает и объединяет все CSV файлы с данными о заказах"""
    print("📊 Загрузка данных для RF Matrix анализа...")
    
    # Список файлов для загрузки
    files = [
        'orders_info_ru_01-2025.csv',
        'orders_info_ru_02-2025.csv', 
        'orders_info_ru_03-2025.csv',
        'orders_info_ru_04-2025.csv',
        'orders_info_ru_05-2025.csv'
    ]
    
    dataframes = []
    for file in files:
        try:
            df = pd.read_csv(file)
            df['month'] = file.split('_')[-1].split('-')[0]  # Извлекаем месяц из имени файла
            dataframes.append(df)
            print(f"✅ Загружен {file}: {len(df)} записей")
        except Exception as e:
            print(f"❌ Ошибка загрузки {file}: {e}")
    
    # Объединяем все данные
    combined_df = pd.concat(dataframes, ignore_index=True)
    print(f"\n🎯 Общий объем данных: {len(combined_df)} записей")
    
    # Преобразуем дату заказа
    combined_df['order_date'] = pd.to_datetime(combined_df['order_date'], format='%d.%m.%Y')
    
    return combined_df

def calculate_rf_metrics(df):
    """Рассчитывает RF метрики для каждого продавца"""
    print("\n" + "="*60)
    print("📈 РАСЧЕТ RF МЕТРИК")
    print("="*60)
    
    # Определяем дату анализа (последняя дата в данных + 1 день)
    analysis_date = df['order_date'].max() + timedelta(days=1)
    print(f"📅 Дата анализа: {analysis_date.strftime('%d.%m.%Y')}")
    
    # Группируем по продавцам и рассчитываем метрики
    rf_data = df.groupby('seller_id').agg({
        'order_date': ['min', 'max', 'count'],
        'sum': ['sum', 'mean'],
        'seller_rating': 'first'
    }).round(2)
    
    # Упрощаем названия колонок
    rf_data.columns = [
        'first_order_date', 'last_order_date', 'frequency',
        'total_revenue', 'avg_order_value', 'seller_rating'
    ]
    
    # Рассчитываем Recency (дни с последнего заказа)
    rf_data['recency_days'] = (analysis_date - rf_data['last_order_date']).dt.days
    
    # Рассчитываем дополнительные метрики
    rf_data['customer_lifetime_days'] = (
        rf_data['last_order_date'] - rf_data['first_order_date']
    ).dt.days + 1
    
    rf_data['orders_per_day'] = rf_data['frequency'] / rf_data['customer_lifetime_days']
    rf_data['revenue_per_day'] = rf_data['total_revenue'] / rf_data['customer_lifetime_days']
    
    print(f"📊 Рассчитаны RF метрики для {len(rf_data)} продавцов")
    
    # Базовая статистика
    print(f"\n📈 Статистика RF метрик:")
    print(f"  Recency (дни с последнего заказа):")
    print(f"    Минимум: {rf_data['recency_days'].min()} дней")
    print(f"    Максимум: {rf_data['recency_days'].max()} дней")
    print(f"    Среднее: {rf_data['recency_days'].mean():.1f} дней")
    print(f"    Медиана: {rf_data['recency_days'].median():.1f} дней")
    
    print(f"\n  Frequency (количество заказов):")
    print(f"    Минимум: {rf_data['frequency'].min()} заказов")
    print(f"    Максимум: {rf_data['frequency'].max()} заказов")
    print(f"    Среднее: {rf_data['frequency'].mean():.1f} заказов")
    print(f"    Медиана: {rf_data['frequency'].median():.1f} заказов")
    
    return rf_data

def create_rf_scores(rf_data):
    """Создает RF скоры (1-5) для каждого продавца"""
    print("\n📊 Создание RF скоров...")
    
    # Создаем скоры Recency (1 = давно, 5 = недавно)
    rf_data['recency_score'] = pd.qcut(
        rf_data['recency_days'], 
        5, 
        labels=[5, 4, 3, 2, 1]  # Инвертируем: меньше дней = выше скор
    ).astype(int)
    
    # Создаем скоры Frequency (1 = редко, 5 = часто)
    rf_data['frequency_score'] = pd.qcut(
        rf_data['frequency'].rank(method='first'), 
        5, 
        labels=[1, 2, 3, 4, 5]
    ).astype(int)
    
    # Создаем комбинированный RF скор
    rf_data['rf_score'] = rf_data['recency_score'] * 10 + rf_data['frequency_score']
    
    # Создаем текстовые сегменты
    def get_rf_segment(row):
        r, f = row['recency_score'], row['frequency_score']
        
        if r >= 4 and f >= 4:
            return 'Champions'
        elif r >= 3 and f >= 4:
            return 'Loyal Customers'
        elif r >= 4 and f >= 2:
            return 'Potential Loyalists'
        elif r >= 4 and f == 1:
            return 'New Customers'
        elif r >= 2 and f >= 3:
            return 'Promising'
        elif r >= 2 and f >= 2:
            return 'Need Attention'
        elif r >= 3 and f == 1:
            return 'About to Sleep'
        elif r >= 1 and f >= 2:
            return 'At Risk'
        elif r == 1 and f >= 4:
            return 'Cannot Lose Them'
        else:
            return 'Lost'
    
    rf_data['rf_segment'] = rf_data.apply(get_rf_segment, axis=1)
    
    print("✅ RF скоры созданы")
    
    return rf_data

def create_rf_matrix(rf_data):
    """Создает RF матрицу"""
    print("\n" + "="*60)
    print("📊 СОЗДАНИЕ RF МАТРИЦЫ")
    print("="*60)
    
    # Создаем матрицу подсчета продавцов
    rf_matrix_count = rf_data.pivot_table(
        index='recency_score',
        columns='frequency_score', 
        values='seller_rating',
        aggfunc='count',
        fill_value=0
    )
    
    # Создаем матрицу средней выручки
    rf_matrix_revenue = rf_data.pivot_table(
        index='recency_score',
        columns='frequency_score', 
        values='total_revenue',
        aggfunc='mean',
        fill_value=0
    ).round(0)
    
    # Создаем матрицу общей выручки
    rf_matrix_total_revenue = rf_data.pivot_table(
        index='recency_score',
        columns='frequency_score', 
        values='total_revenue',
        aggfunc='sum',
        fill_value=0
    ).round(0)
    
    print("📊 RF Матрица (количество продавцов):")
    print("Строки: Recency Score (5=недавно, 1=давно)")
    print("Столбцы: Frequency Score (5=часто, 1=редко)")
    print(rf_matrix_count)
    
    print("\n💰 RF Матрица (средняя выручка на продавца):")
    print(rf_matrix_revenue)
    
    print("\n💸 RF Матрица (общая выручка по сегментам):")
    print(rf_matrix_total_revenue)
    
    return rf_matrix_count, rf_matrix_revenue, rf_matrix_total_revenue

def analyze_rf_segments(rf_data):
    """Анализирует RF сегменты"""
    print("\n" + "="*60)
    print("🎯 АНАЛИЗ RF СЕГМЕНТОВ")
    print("="*60)
    
    # Анализ по сегментам
    segment_analysis = rf_data.groupby('rf_segment').agg({
        'seller_rating': 'count',
        'total_revenue': ['sum', 'mean'],
        'frequency': 'mean',
        'recency_days': 'mean',
        'avg_order_value': 'mean'
    }).round(2)
    
    segment_analysis.columns = [
        'count', 'total_revenue', 'avg_revenue_per_seller',
        'avg_frequency', 'avg_recency_days', 'avg_order_value'
    ]
    
    # Сортируем по общей выручке
    segment_analysis = segment_analysis.sort_values('total_revenue', ascending=False)
    
    print("📈 Анализ RF сегментов:")
    print(f"{'Сегмент':<20} {'Кол-во':<8} {'Общая выручка':<15} {'Средняя выручка':<15} {'Частота':<10} {'Recency':<10}")
    print("-" * 85)
    
    for segment, data in segment_analysis.iterrows():
        print(f"{segment:<20} {data['count']:<8.0f} {data['total_revenue']:<15,.0f} "
              f"{data['avg_revenue_per_seller']:<15,.0f} {data['avg_frequency']:<10.1f} {data['avg_recency_days']:<10.1f}")
    
    # Процентное распределение
    total_sellers = len(rf_data)
    total_revenue = rf_data['total_revenue'].sum()
    
    print(f"\n📊 Процентное распределение:")
    print(f"{'Сегмент':<20} {'% продавцов':<15} {'% выручки':<15}")
    print("-" * 50)
    
    for segment, data in segment_analysis.iterrows():
        seller_pct = (data['count'] / total_sellers) * 100
        revenue_pct = (data['total_revenue'] / total_revenue) * 100
        print(f"{segment:<20} {seller_pct:<15.1f} {revenue_pct:<15.1f}")
    
    return segment_analysis

def visualize_rf_matrix(rf_data, rf_matrix_count, rf_matrix_revenue):
    """Создает визуализации RF матрицы"""
    print("\n📊 Создание визуализаций RF матрицы...")
    
    # Настройка стиля
    plt.style.use('default')
    sns.set_palette("viridis")
    
    # Создаем фигуру с подграфиками
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('RF Matrix Analysis Dashboard', fontsize=16, fontweight='bold')
    
    # 1. Heatmap количества продавцов
    sns.heatmap(rf_matrix_count, annot=True, fmt='d', cmap='Blues', ax=axes[0, 0])
    axes[0, 0].set_title('RF Matrix: Количество продавцов')
    axes[0, 0].set_xlabel('Frequency Score (1=редко, 5=часто)')
    axes[0, 0].set_ylabel('Recency Score (1=давно, 5=недавно)')
    
    # 2. Heatmap средней выручки
    sns.heatmap(rf_matrix_revenue, annot=True, fmt='.0f', cmap='Reds', ax=axes[0, 1])
    axes[0, 1].set_title('RF Matrix: Средняя выручка на продавца')
    axes[0, 1].set_xlabel('Frequency Score (1=редко, 5=часто)')
    axes[0, 1].set_ylabel('Recency Score (1=давно, 5=недавно)')
    
    # 3. Scatter plot R vs F
    scatter = axes[1, 0].scatter(
        rf_data['frequency'], 
        rf_data['recency_days'], 
        c=rf_data['total_revenue'], 
        cmap='viridis', 
        alpha=0.6,
        s=30
    )
    axes[1, 0].set_xlabel('Frequency (количество заказов)')
    axes[1, 0].set_ylabel('Recency (дни с последнего заказа)')
    axes[1, 0].set_title('Scatter Plot: Recency vs Frequency')
    plt.colorbar(scatter, ax=axes[1, 0], label='Общая выручка')
    
    # 4. Распределение по сегментам
    segment_counts = rf_data['rf_segment'].value_counts()
    axes[1, 1].pie(segment_counts.values, labels=segment_counts.index, autopct='%1.1f%%')
    axes[1, 1].set_title('Распределение продавцов по RF сегментам')
    
    plt.tight_layout()
    plt.savefig('rf_matrix_dashboard.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Визуализации сохранены в rf_matrix_dashboard.png")

def save_rf_results(rf_data, segment_analysis, rf_matrix_count, rf_matrix_revenue):
    """Сохраняет результаты RF анализа"""
    print("\n💾 Сохранение результатов...")
    
    # Основные RF данные
    rf_export = rf_data[[
        'recency_days', 'frequency', 'total_revenue', 'avg_order_value',
        'recency_score', 'frequency_score', 'rf_score', 'rf_segment',
        'seller_rating', 'customer_lifetime_days'
    ]].copy()
    
    rf_export.to_csv('rf_analysis_detailed.csv')
    print("✅ Детальный RF анализ сохранен: rf_analysis_detailed.csv")
    
    # Анализ сегментов
    segment_analysis.to_csv('rf_segments_analysis.csv')
    print("✅ Анализ сегментов сохранен: rf_segments_analysis.csv")
    
    # RF матрицы
    rf_matrix_count.to_csv('rf_matrix_count.csv')
    rf_matrix_revenue.to_csv('rf_matrix_revenue.csv')
    print("✅ RF матрицы сохранены: rf_matrix_count.csv, rf_matrix_revenue.csv")
    
    return True

def get_top_segments_insights(rf_data):
    """Получает инсайты по топ сегментам"""
    print("\n" + "="*60)
    print("💡 ИНСАЙТЫ ПО КЛЮЧЕВЫМ СЕГМЕНТАМ")
    print("="*60)

    # Champions (R=5, F=5 или R=4, F=5)
    champions = rf_data[rf_data['rf_segment'] == 'Champions']
    if len(champions) > 0:
        print(f"\n🏆 CHAMPIONS ({len(champions)} продавцов):")
        print(f"  Средняя выручка: {champions['total_revenue'].mean():,.0f} руб.")
        print(f"  Средняя частота: {champions['frequency'].mean():.1f} заказов")
        print(f"  Средний recency: {champions['recency_days'].mean():.1f} дней")
        print(f"  Общая выручка: {champions['total_revenue'].sum():,.0f} руб.")

        # Топ-5 Champions
        top_champions = champions.nlargest(5, 'total_revenue')
        print(f"  Топ-5 Champions:")
        for idx, (seller_id, data) in enumerate(top_champions.iterrows(), 1):
            print(f"    {idx}. ID: {seller_id}, Выручка: {data['total_revenue']:,.0f} руб., "
                  f"Заказов: {data['frequency']}, Recency: {data['recency_days']} дней")

    # At Risk
    at_risk = rf_data[rf_data['rf_segment'] == 'At Risk']
    if len(at_risk) > 0:
        print(f"\n⚠️  AT RISK ({len(at_risk)} продавцов):")
        print(f"  Средняя выручка: {at_risk['total_revenue'].mean():,.0f} руб.")
        print(f"  Средняя частота: {at_risk['frequency'].mean():.1f} заказов")
        print(f"  Средний recency: {at_risk['recency_days'].mean():.1f} дней")
        print(f"  Общая выручка: {at_risk['total_revenue'].sum():,.0f} руб.")

    # Lost
    lost = rf_data[rf_data['rf_segment'] == 'Lost']
    if len(lost) > 0:
        print(f"\n❌ LOST ({len(lost)} продавцов):")
        print(f"  Средняя выручка: {lost['total_revenue'].mean():,.0f} руб.")
        print(f"  Средняя частота: {lost['frequency'].mean():.1f} заказов")
        print(f"  Средний recency: {lost['recency_days'].mean():.1f} дней")
        print(f"  Общая выручка: {lost['total_revenue'].sum():,.0f} руб.")

def main():
    """Основная функция для RF Matrix анализа"""
    print("🚀 ЗАПУСК RF MATRIX АНАЛИЗА")
    print("="*60)

    try:
        # 1. Загрузка данных
        df = load_and_combine_data()

        # 2. Расчет RF метрик
        rf_data = calculate_rf_metrics(df)

        # 3. Создание RF скоров
        rf_data = create_rf_scores(rf_data)

        # 4. Создание RF матрицы
        rf_matrix_count, rf_matrix_revenue, rf_matrix_total_revenue = create_rf_matrix(rf_data)

        # 5. Анализ сегментов
        segment_analysis = analyze_rf_segments(rf_data)

        # 6. Инсайты по ключевым сегментам
        get_top_segments_insights(rf_data)

        # 7. Визуализация
        visualize_rf_matrix(rf_data, rf_matrix_count, rf_matrix_revenue)

        # 8. Сохранение результатов
        save_rf_results(rf_data, segment_analysis, rf_matrix_count, rf_matrix_revenue)

        print("\n🎉 RF MATRIX АНАЛИЗ ЗАВЕРШЕН УСПЕШНО!")
        print("="*60)
        print(f"📊 Проанализировано продавцов: {len(rf_data)}")
        print(f"📈 Создано сегментов: {rf_data['rf_segment'].nunique()}")
        print(f"💰 Общая выручка: {rf_data['total_revenue'].sum():,.0f} руб.")

        # Краткая сводка по матрице
        print(f"\n📊 RF MATRIX СВОДКА:")
        print(f"  Размер матрицы: 5x5 (Recency x Frequency)")
        print(f"  Всего ячеек: 25")
        print(f"  Заполненных ячеек: {(rf_matrix_count > 0).sum().sum()}")
        print(f"  Самая популярная ячейка: R{rf_matrix_count.stack().idxmax()[0]}F{rf_matrix_count.stack().idxmax()[1]} "
              f"({rf_matrix_count.max().max()} продавцов)")

        return df, rf_data, rf_matrix_count, rf_matrix_revenue, segment_analysis

    except Exception as e:
        print(f"❌ Ошибка при выполнении RF анализа: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None, None

if __name__ == "__main__":
    # Запуск RF Matrix анализа
    data, rf_results, matrix_count, matrix_revenue, segments = main()
