import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_and_combine_data():
    """Загружает и объединяет все CSV файлы с данными о заказах"""
    print("📊 Загрузка данных для LTV forecasting...")
    
    # Список файлов для загрузки
    files = [
        'orders_info_ru_01-2025.csv',
        'orders_info_ru_02-2025.csv', 
        'orders_info_ru_03-2025.csv',
        'orders_info_ru_04-2025.csv',
        'orders_info_ru_05-2025.csv'
    ]
    
    dataframes = []
    for file in files:
        try:
            df = pd.read_csv(file)
            df['month'] = file.split('_')[-1].split('-')[0]  # Извлекаем месяц из имени файла
            dataframes.append(df)
            print(f"✅ Загружен {file}: {len(df)} записей")
        except Exception as e:
            print(f"❌ Ошибка загрузки {file}: {e}")
    
    # Объединяем все данные
    combined_df = pd.concat(dataframes, ignore_index=True)
    print(f"\n🎯 Общий объем данных: {len(combined_df)} записей")
    
    # Преобразуем дату заказа
    combined_df['order_date'] = pd.to_datetime(combined_df['order_date'], format='%d.%m.%Y')
    combined_df['month_num'] = combined_df['order_date'].dt.month
    
    return combined_df

def calculate_seller_metrics(df):
    """Рассчитывает ключевые метрики для каждого продавца"""
    print("\n" + "="*60)
    print("📈 РАСЧЕТ КЛЮЧЕВЫХ МЕТРИК ДЛЯ LTV")
    print("="*60)
    
    # Группируем по продавцам
    seller_metrics = df.groupby('seller_id').agg({
        'sum': ['sum', 'mean', 'count', 'std'],
        'order_date': ['min', 'max'],
        'seller_rating': 'first',
        'month_num': 'nunique',
        'category_id': 'nunique',
        'region_id': 'nunique',
        'views_count': 'mean',
        'success_fee': 'sum'
    }).round(2)
    
    # Упрощаем названия колонок
    seller_metrics.columns = [
        'total_revenue', 'avg_order_value', 'total_orders', 'revenue_std',
        'first_order_date', 'last_order_date', 'seller_rating',
        'active_months', 'categories_count', 'regions_count',
        'avg_views', 'total_fees'
    ]
    
    # Рассчитываем дополнительные метрики
    seller_metrics['customer_lifetime_days'] = (
        seller_metrics['last_order_date'] - seller_metrics['first_order_date']
    ).dt.days + 1
    
    seller_metrics['orders_per_month'] = (
        seller_metrics['total_orders'] / seller_metrics['active_months']
    ).fillna(0)
    
    seller_metrics['revenue_per_month'] = (
        seller_metrics['total_revenue'] / seller_metrics['active_months']
    ).fillna(0)
    
    # Коэффициент вариации выручки (стабильность)
    seller_metrics['revenue_cv'] = (
        seller_metrics['revenue_std'] / seller_metrics['avg_order_value']
    ).fillna(0)
    
    # Рентабельность (выручка минус комиссии)
    seller_metrics['net_revenue'] = seller_metrics['total_revenue'] - seller_metrics['total_fees']
    seller_metrics['profit_margin'] = (
        seller_metrics['net_revenue'] / seller_metrics['total_revenue']
    ).fillna(0)
    
    print(f"📊 Рассчитаны метрики для {len(seller_metrics)} продавцов")
    
    return seller_metrics

def calculate_rfm_metrics(df, seller_metrics):
    """Рассчитывает RFM метрики (Recency, Frequency, Monetary)"""
    print("\n📊 Расчет RFM метрик...")
    
    # Определяем дату анализа (последняя дата в данных + 1 день)
    analysis_date = df['order_date'].max() + timedelta(days=1)
    
    # Recency: дни с последнего заказа
    seller_metrics['recency_days'] = (
        analysis_date - seller_metrics['last_order_date']
    ).dt.days
    
    # Frequency: уже есть как total_orders
    seller_metrics['frequency'] = seller_metrics['total_orders']
    
    # Monetary: уже есть как total_revenue
    seller_metrics['monetary'] = seller_metrics['total_revenue']
    
    # Создаем RFM сегменты
    seller_metrics['recency_score'] = pd.qcut(
        seller_metrics['recency_days'], 5, labels=[5,4,3,2,1]
    ).astype(int)
    
    seller_metrics['frequency_score'] = pd.qcut(
        seller_metrics['frequency'].rank(method='first'), 5, labels=[1,2,3,4,5]
    ).astype(int)
    
    seller_metrics['monetary_score'] = pd.qcut(
        seller_metrics['monetary'].rank(method='first'), 5, labels=[1,2,3,4,5]
    ).astype(int)
    
    # Общий RFM скор
    seller_metrics['rfm_score'] = (
        seller_metrics['recency_score'] * 100 + 
        seller_metrics['frequency_score'] * 10 + 
        seller_metrics['monetary_score']
    )
    
    return seller_metrics

def predict_ltv_linear(seller_metrics):
    """Прогнозирует LTV используя линейную регрессию"""
    print("\n🔮 Прогнозирование LTV (Линейная регрессия)...")
    
    # Подготавливаем признаки для модели
    features = [
        'avg_order_value', 'total_orders', 'active_months',
        'orders_per_month', 'revenue_per_month', 'seller_rating',
        'categories_count', 'regions_count', 'avg_views',
        'recency_days', 'frequency_score', 'monetary_score'
    ]
    
    # Удаляем строки с NaN
    model_data = seller_metrics[features + ['total_revenue']].dropna()
    
    if len(model_data) < 10:
        print("❌ Недостаточно данных для обучения модели")
        return seller_metrics
    
    X = model_data[features]
    y = model_data['total_revenue']
    
    # Разделяем на обучающую и тестовую выборки
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Обучаем модель
    model = LinearRegression()
    model.fit(X_train, y_train)
    
    # Делаем прогнозы
    y_pred = model.predict(X_test)
    
    # Оцениваем качество модели
    mae = mean_absolute_error(y_test, y_pred)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    print(f"📈 Качество модели (Линейная регрессия):")
    print(f"  MAE: {mae:,.2f}")
    print(f"  MSE: {mse:,.2f}")
    print(f"  R²: {r2:.3f}")
    
    # Прогнозируем LTV для всех продавцов
    X_all = seller_metrics[features].fillna(0)
    ltv_predictions = model.predict(X_all)
    
    seller_metrics['predicted_ltv_linear'] = ltv_predictions
    
    return seller_metrics, model

def predict_ltv_rf(seller_metrics):
    """Прогнозирует LTV используя Random Forest"""
    print("\n🌲 Прогнозирование LTV (Random Forest)...")
    
    # Подготавливаем признаки для модели
    features = [
        'avg_order_value', 'total_orders', 'active_months',
        'orders_per_month', 'revenue_per_month', 'seller_rating',
        'categories_count', 'regions_count', 'avg_views',
        'recency_days', 'frequency_score', 'monetary_score',
        'revenue_cv', 'profit_margin'
    ]
    
    # Удаляем строки с NaN
    model_data = seller_metrics[features + ['total_revenue']].dropna()
    
    if len(model_data) < 10:
        print("❌ Недостаточно данных для обучения модели")
        return seller_metrics
    
    X = model_data[features]
    y = model_data['total_revenue']
    
    # Разделяем на обучающую и тестовую выборки
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Обучаем модель
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # Делаем прогнозы
    y_pred = model.predict(X_test)
    
    # Оцениваем качество модели
    mae = mean_absolute_error(y_test, y_pred)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    print(f"📈 Качество модели (Random Forest):")
    print(f"  MAE: {mae:,.2f}")
    print(f"  MSE: {mse:,.2f}")
    print(f"  R²: {r2:.3f}")
    
    # Важность признаков
    feature_importance = pd.DataFrame({
        'feature': features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\n🔍 Важность признаков (топ-5):")
    for _, row in feature_importance.head().iterrows():
        print(f"  {row['feature']}: {row['importance']:.3f}")
    
    # Прогнозируем LTV для всех продавцов
    X_all = seller_metrics[features].fillna(0)
    ltv_predictions = model.predict(X_all)
    
    seller_metrics['predicted_ltv_rf'] = ltv_predictions
    
    return seller_metrics, model

def calculate_cohort_ltv(df):
    """Рассчитывает LTV по когортам (месяц первого заказа)"""
    print("\n👥 Анализ LTV по когортам...")

    # Определяем когорту для каждого продавца (месяц первого заказа)
    seller_cohorts = df.groupby('seller_id')['order_date'].min().reset_index()
    seller_cohorts['cohort_month'] = seller_cohorts['order_date'].dt.to_period('M')

    # Добавляем когорту к основным данным
    df_with_cohorts = df.merge(seller_cohorts[['seller_id', 'cohort_month']], on='seller_id')

    # Рассчитываем LTV по когортам
    cohort_ltv = df_with_cohorts.groupby('cohort_month').agg({
        'sum': ['sum', 'mean', 'count'],
        'seller_id': 'nunique'
    }).round(2)

    cohort_ltv.columns = ['total_revenue', 'avg_revenue_per_order', 'total_orders', 'unique_sellers']
    cohort_ltv['avg_ltv_per_seller'] = cohort_ltv['total_revenue'] / cohort_ltv['unique_sellers']

    print(f"📊 Анализ по {len(cohort_ltv)} когортам:")
    for cohort, data in cohort_ltv.iterrows():
        print(f"  {cohort}: {data['unique_sellers']} продавцов, средний LTV: {data['avg_ltv_per_seller']:,.2f} руб.")

    return cohort_ltv

def segment_sellers_by_ltv(seller_metrics):
    """Сегментирует продавцов по прогнозируемому LTV"""
    print("\n🎯 Сегментация продавцов по LTV...")

    # Используем лучшую модель (Random Forest, если доступна)
    ltv_column = 'predicted_ltv_rf' if 'predicted_ltv_rf' in seller_metrics.columns else 'predicted_ltv_linear'

    if ltv_column not in seller_metrics.columns:
        print("❌ Нет прогнозов LTV для сегментации")
        return seller_metrics

    # Определяем квантили для сегментации
    q25 = seller_metrics[ltv_column].quantile(0.25)
    q50 = seller_metrics[ltv_column].quantile(0.50)
    q75 = seller_metrics[ltv_column].quantile(0.75)
    q90 = seller_metrics[ltv_column].quantile(0.90)

    # Создаем сегменты
    def assign_ltv_segment(ltv_value):
        if ltv_value >= q90:
            return 'VIP'
        elif ltv_value >= q75:
            return 'High'
        elif ltv_value >= q50:
            return 'Medium'
        elif ltv_value >= q25:
            return 'Low'
        else:
            return 'At Risk'

    seller_metrics['ltv_segment'] = seller_metrics[ltv_column].apply(assign_ltv_segment)

    # Анализ сегментов
    segment_analysis = seller_metrics.groupby('ltv_segment').agg({
        ltv_column: ['count', 'mean', 'sum'],
        'total_revenue': ['mean', 'sum'],
        'total_orders': 'mean',
        'seller_rating': 'mean'
    }).round(2)

    print(f"📈 Сегментация по LTV:")
    for segment in ['VIP', 'High', 'Medium', 'Low', 'At Risk']:
        if segment in segment_analysis.index:
            count = segment_analysis.loc[segment, (ltv_column, 'count')]
            avg_ltv = segment_analysis.loc[segment, (ltv_column, 'mean')]
            total_ltv = segment_analysis.loc[segment, (ltv_column, 'sum')]
            print(f"  {segment}: {count} продавцов, средний LTV: {avg_ltv:,.2f}, общий LTV: {total_ltv:,.2f}")

    return seller_metrics

def identify_high_potential_sellers(seller_metrics):
    """Выявляет продавцов с высоким потенциалом роста"""
    print("\n🚀 Выявление продавцов с высоким потенциалом...")

    # Критерии высокого потенциала:
    # 1. Низкий текущий LTV, но высокие показатели активности
    # 2. Высокий рейтинг
    # 3. Недавно зарегистрированы
    # 4. Растущая активность

    ltv_column = 'predicted_ltv_rf' if 'predicted_ltv_rf' in seller_metrics.columns else 'predicted_ltv_linear'

    if ltv_column not in seller_metrics.columns:
        print("❌ Нет прогнозов LTV для анализа потенциала")
        return pd.DataFrame()

    # Нормализуем метрики для скоринга
    seller_metrics['ltv_percentile'] = seller_metrics[ltv_column].rank(pct=True)
    seller_metrics['rating_percentile'] = seller_metrics['seller_rating'].rank(pct=True)
    seller_metrics['activity_percentile'] = seller_metrics['orders_per_month'].rank(pct=True)
    seller_metrics['recency_percentile'] = (1 - seller_metrics['recency_days'].rank(pct=True))  # Инвертируем

    # Рассчитываем потенциальный скор
    seller_metrics['potential_score'] = (
        seller_metrics['rating_percentile'] * 0.3 +
        seller_metrics['activity_percentile'] * 0.3 +
        seller_metrics['recency_percentile'] * 0.2 +
        (1 - seller_metrics['ltv_percentile']) * 0.2  # Низкий текущий LTV = высокий потенциал
    )

    # Выбираем топ-20% по потенциалу
    high_potential = seller_metrics[
        seller_metrics['potential_score'] >= seller_metrics['potential_score'].quantile(0.8)
    ].copy()

    high_potential = high_potential.sort_values('potential_score', ascending=False)

    print(f"🎯 Найдено {len(high_potential)} продавцов с высоким потенциалом")
    print(f"📊 Средний потенциальный скор: {high_potential['potential_score'].mean():.3f}")

    return high_potential

def create_ltv_visualizations(seller_metrics, cohort_ltv):
    """Создает визуализации для LTV анализа"""
    print("\n📊 Создание визуализаций...")

    # Настройка стиля
    plt.style.use('default')
    sns.set_palette("husl")

    # 1. Распределение LTV
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('LTV Analysis Dashboard', fontsize=16, fontweight='bold')

    # Распределение текущего LTV
    axes[0, 0].hist(seller_metrics['total_revenue'], bins=50, alpha=0.7, edgecolor='black')
    axes[0, 0].set_title('Распределение текущего LTV')
    axes[0, 0].set_xlabel('LTV (руб.)')
    axes[0, 0].set_ylabel('Количество продавцов')
    axes[0, 0].grid(True, alpha=0.3)

    # Прогнозируемый vs текущий LTV
    if 'predicted_ltv_rf' in seller_metrics.columns:
        axes[0, 1].scatter(seller_metrics['total_revenue'], seller_metrics['predicted_ltv_rf'], alpha=0.6)
        axes[0, 1].plot([0, seller_metrics['total_revenue'].max()], [0, seller_metrics['total_revenue'].max()], 'r--')
        axes[0, 1].set_title('Прогнозируемый vs Текущий LTV')
        axes[0, 1].set_xlabel('Текущий LTV')
        axes[0, 1].set_ylabel('Прогнозируемый LTV')
        axes[0, 1].grid(True, alpha=0.3)

    # Сегменты LTV
    if 'ltv_segment' in seller_metrics.columns:
        segment_counts = seller_metrics['ltv_segment'].value_counts()
        axes[1, 0].pie(segment_counts.values, labels=segment_counts.index, autopct='%1.1f%%')
        axes[1, 0].set_title('Распределение по сегментам LTV')

    # LTV по когортам
    if not cohort_ltv.empty:
        cohort_ltv_plot = cohort_ltv['avg_ltv_per_seller'].reset_index()
        axes[1, 1].plot(range(len(cohort_ltv_plot)), cohort_ltv_plot['avg_ltv_per_seller'], marker='o')
        axes[1, 1].set_title('Средний LTV по когортам')
        axes[1, 1].set_xlabel('Когорта (по порядку)')
        axes[1, 1].set_ylabel('Средний LTV')
        axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('ltv_analysis_dashboard.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ Визуализации сохранены в ltv_analysis_dashboard.png")

def save_ltv_results(seller_metrics, cohort_ltv, high_potential):
    """Сохраняет результаты LTV анализа"""
    print("\n💾 Сохранение результатов...")

    # Основные результаты LTV
    ltv_results = seller_metrics[[
        'total_revenue', 'predicted_ltv_linear', 'predicted_ltv_rf',
        'ltv_segment', 'potential_score', 'rfm_score',
        'avg_order_value', 'total_orders', 'seller_rating',
        'recency_days', 'frequency_score', 'monetary_score'
    ]].copy()

    ltv_results.to_csv('seller_ltv_analysis.csv')
    print("✅ Основные результаты сохранены: seller_ltv_analysis.csv")

    # Когортный анализ
    cohort_ltv.to_csv('cohort_ltv_analysis.csv')
    print("✅ Когортный анализ сохранен: cohort_ltv_analysis.csv")

    # Продавцы с высоким потенциалом
    if not high_potential.empty:
        high_potential_export = high_potential[[
            'total_revenue', 'predicted_ltv_rf', 'potential_score',
            'seller_rating', 'total_orders', 'orders_per_month'
        ]].copy()
        high_potential_export.to_csv('high_potential_sellers.csv')
        print("✅ Продавцы с высоким потенциалом сохранены: high_potential_sellers.csv")

    return True

def main():
    """Основная функция для LTV forecasting"""
    print("🚀 ЗАПУСК LTV FORECASTING ДЛЯ ВСЕХ ПРОДАВЦОВ")
    print("="*70)

    try:
        # 1. Загрузка данных
        df = load_and_combine_data()

        # 2. Расчет базовых метрик
        seller_metrics = calculate_seller_metrics(df)

        # 3. Расчет RFM метрик
        seller_metrics = calculate_rfm_metrics(df, seller_metrics)

        # 4. Прогнозирование LTV (линейная регрессия)
        seller_metrics, linear_model = predict_ltv_linear(seller_metrics)

        # 5. Прогнозирование LTV (Random Forest)
        seller_metrics, rf_model = predict_ltv_rf(seller_metrics)

        # 6. Когортный анализ
        cohort_ltv = calculate_cohort_ltv(df)

        # 7. Сегментация по LTV
        seller_metrics = segment_sellers_by_ltv(seller_metrics)

        # 8. Выявление продавцов с высоким потенциалом
        high_potential = identify_high_potential_sellers(seller_metrics)

        # 9. Создание визуализаций
        create_ltv_visualizations(seller_metrics, cohort_ltv)

        # 10. Сохранение результатов
        save_ltv_results(seller_metrics, cohort_ltv, high_potential)

        # Итоговая статистика
        print("\n🎉 LTV FORECASTING ЗАВЕРШЕН УСПЕШНО!")
        print("="*70)
        print(f"📊 Проанализировано продавцов: {len(seller_metrics)}")
        print(f"📈 Общий LTV всех продавцов: {seller_metrics['total_revenue'].sum():,.2f} руб.")
        print(f"🔮 Прогнозируемый LTV (RF): {seller_metrics['predicted_ltv_rf'].sum():,.2f} руб.")
        print(f"🎯 Продавцов с высоким потенциалом: {len(high_potential)}")

        # Топ-10 по прогнозируемому LTV
        print(f"\n🏆 ТОП-10 ПРОДАВЦОВ ПО ПРОГНОЗИРУЕМОМУ LTV:")
        top_ltv = seller_metrics.nlargest(10, 'predicted_ltv_rf')
        for i, (seller_id, data) in enumerate(top_ltv.iterrows(), 1):
            print(f"  {i:2d}. ID: {seller_id}")
            print(f"      Текущий LTV: {data['total_revenue']:,.2f} руб.")
            print(f"      Прогноз LTV: {data['predicted_ltv_rf']:,.2f} руб.")
            print(f"      Сегмент: {data['ltv_segment']}")
            print()

        return df, seller_metrics, cohort_ltv, high_potential

    except Exception as e:
        print(f"❌ Ошибка при выполнении LTV forecasting: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

if __name__ == "__main__":
    # Запуск LTV forecasting
    data, metrics, cohorts, potential = main()
