import pandas as pd
import numpy as np

def load_and_combine_data():
    """Загружает и объединяет все CSV файлы с данными о заказах"""
    print("📊 Загрузка данных...")
    
    # Список файлов для загрузки
    files = [
        'orders_info_ru_01-2025.csv',
        'orders_info_ru_02-2025.csv', 
        'orders_info_ru_03-2025.csv',
        'orders_info_ru_04-2025.csv',
        'orders_info_ru_05-2025.csv'
    ]
    
    dataframes = []
    for file in files:
        try:
            df = pd.read_csv(file)
            df['month'] = file.split('_')[-1].split('-')[0]  # Извлекаем месяц из имени файла
            dataframes.append(df)
            print(f"✅ Загружен {file}: {len(df)} записей")
        except Exception as e:
            print(f"❌ Ошибка загрузки {file}: {e}")
    
    # Объединяем все данные
    combined_df = pd.concat(dataframes, ignore_index=True)
    print(f"\n🎯 Общий объем данных: {len(combined_df)} записей")
    return combined_df

def filter_expensive_orders(df, price_threshold=600000):
    """Фильтрует заказы дороже указанной суммы"""
    print("\n" + "="*60)
    print(f"💰 ФИЛЬТРАЦИЯ ДОРОГИХ ЗАКАЗОВ (>{price_threshold:,})")
    print("="*60)
    
    # Фильтруем заказы дороже порога
    expensive_orders = df[df['sum'] > price_threshold].copy()
    
    print(f"📊 Найдено дорогих заказов: {len(expensive_orders)} из {len(df)} ({len(expensive_orders)/len(df)*100:.2f}%)")
    
    if len(expensive_orders) > 0:
        print(f"\n📈 Статистика дорогих заказов:")
        print(f"  Минимальная сумма: {expensive_orders['sum'].min():,}")
        print(f"  Максимальная сумма: {expensive_orders['sum'].max():,}")
        print(f"  Средняя сумма: {expensive_orders['sum'].mean():,.2f}")
        print(f"  Медианная сумма: {expensive_orders['sum'].median():,}")
        
        # Топ-10 самых дорогих заказов
        print(f"\n🏆 ТОП-10 САМЫХ ДОРОГИХ ЗАКАЗОВ:")
        top_expensive = expensive_orders.nlargest(10, 'sum')[['id', 'sum', 'order_date', 'category_id', 'region_id']]
        for idx, row in top_expensive.iterrows():
            print(f"  {row['sum']:,} руб. - ID: {row['id']} (дата: {row['order_date']}, категория: {row['category_id']})")
        
        # Анализ по категориям
        print(f"\n📂 РАСПРЕДЕЛЕНИЕ ПО КАТЕГОРИЯМ:")
        category_stats = expensive_orders.groupby('category_id').agg({
            'sum': ['count', 'mean', 'max'],
            'id': 'count'
        }).round(2)
        category_stats.columns = ['Количество', 'Средняя_сумма', 'Макс_сумма', 'Заказов']
        category_stats = category_stats.sort_values('Количество', ascending=False)
        print(category_stats.head(10))
        
        # Анализ по регионам
        print(f"\n🌍 РАСПРЕДЕЛЕНИЕ ПО РЕГИОНАМ:")
        region_stats = expensive_orders.groupby('region_id').agg({
            'sum': ['count', 'mean', 'max'],
            'id': 'count'
        }).round(2)
        region_stats.columns = ['Количество', 'Средняя_сумма', 'Макс_сумма', 'Заказов']
        region_stats = region_stats.sort_values('Количество', ascending=False)
        print(region_stats.head(10))
        
        # Анализ по месяцам
        print(f"\n📅 РАСПРЕДЕЛЕНИЕ ПО МЕСЯЦАМ:")
        month_stats = expensive_orders.groupby('month').agg({
            'sum': ['count', 'mean', 'max'],
            'id': 'count'
        }).round(2)
        month_stats.columns = ['Количество', 'Средняя_сумма', 'Макс_сумма', 'Заказов']
        print(month_stats)
        
    return expensive_orders

def save_expensive_orders(df_expensive, price_threshold):
    """Сохраняет дорогие заказы"""
    if len(df_expensive) > 0:
        filename = f'expensive_orders_over_{price_threshold}.csv'
        df_expensive.to_csv(filename, index=False)
        print(f"\n✅ Дорогие заказы сохранены в файл: {filename}")
        print(f"📊 Размер файла: {len(df_expensive)} записей")
        return filename
    else:
        print("❌ Нет дорогих заказов для сохранения")
        return None

def main():
    """Основная функция для фильтрации дорогих заказов"""
    print("🚀 ФИЛЬТРАЦИЯ ДОРОГИХ ЗАКАЗОВ")
    print("="*60)
    
    try:
        # 1. Загрузка данных
        df = load_and_combine_data()
        
        # 2. Фильтрация дорогих заказов (больше 600,000)
        expensive_orders = filter_expensive_orders(df, price_threshold=600000)
        
        # 3. Сохранение результатов
        filename = save_expensive_orders(expensive_orders, 600000)
        
        print("\n🎉 ФИЛЬТРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!")
        print("="*60)
        if filename:
            print(f"📋 Создан файл: {filename}")
        
        return df, expensive_orders
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении фильтрации: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    # Запуск фильтрации
    original_data, expensive_data = main()
