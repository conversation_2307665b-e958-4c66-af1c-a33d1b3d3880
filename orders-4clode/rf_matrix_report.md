# Отчет: RF Matrix Analysis

## 📊 Общие результаты

### Масштаб анализа:
- **Проанализировано продавцов**: 4,945
- **Общий объем данных**: 212,187 заказов
- **Период анализа**: Январь-Май 2025
- **Дата анализа**: 02.06.2025

### RF Метрики:
- **Recency (дни с последнего заказа)**:
  - Минимум: 1 день
  - Максимум: 152 дня
  - Среднее: 58.4 дня
  - Медиана: 50.0 дней

- **Frequency (количество заказов)**:
  - Минимум: 1 заказ
  - Максимум: 19,199 заказов
  - Среднее: 42.9 заказов
  - Медиана: 3.0 заказа

## 📈 RF Matrix (5x5)

### Количество продавцов по ячейкам:

| R\F | F1 (редко) | F2 | F3 | F4 | F5 (часто) |
|-----|------------|----|----|----|-----------| 
| **R1 (давно)** | 372 | 343 | 168 | 66 | 24 |
| **R2** | 262 | 297 | 252 | 144 | 43 |
| **R3** | 188 | 185 | 273 | 223 | 110 |
| **R4** | 120 | 107 | 191 | 322 | 264 |
| **R5 (недавно)** | 47 | 57 | 105 | 234 | **548** |

### Средняя выручка на продавца (руб.):

| R\F | F1 | F2 | F3 | F4 | F5 |
|-----|----|----|----|----|----| 
| **R1** | 6,789 | **1,671,150** | 21,405 | 32,134 | 19,308 |
| **R2** | 13,877 | 10,652 | 14,683 | 61,126 | 144,356 |
| **R3** | 9,430 | 11,895 | 27,635 | 30,592 | 44,902 |
| **R4** | 7,493 | 7,202 | 16,265 | 48,817 | 70,197 |
| **R5** | 18,950 | 12,355 | 18,080 | 57,636 | **754,392** |

### Ключевые наблюдения:
- **Самая популярная ячейка**: R5F5 (548 продавцов) - недавно активные с высокой частотой
- **Самая доходная ячейка**: R5F5 (754,392 руб. средняя выручка)
- **Аномалия**: R1F2 (1,671,150 руб.) - давно неактивные, но с высокой исторической выручкой

## 🎯 RF Сегментация (9 сегментов)

| Сегмент | Продавцов | % от общего | Общая выручка | % выручки | Средняя выручка | Частота | Recency |
|---------|-----------|-------------|---------------|-----------|-----------------|---------|---------|
| **At Risk** | 601 | 12.2% | 579,384,781 | **52.7%** | 964,035 | 5.1 | 126.8 |
| **Champions** | 1,368 | **27.7%** | 461,144,637 | **41.9%** | 337,094 | 138.4 | 12.2 |
| **Promising** | 712 | 14.4% | 26,254,047 | 2.4% | 36,874 | 10.1 | 72.0 |
| **Loyal Customers** | 333 | 6.7% | 11,761,199 | 1.1% | 35,319 | 30.1 | 48.8 |
| **Potential Loyalists** | 460 | 9.3% | 6,479,813 | 0.6% | 14,087 | 2.2 | 17.6 |
| **Lost** | 634 | 12.8% | 6,161,087 | 0.6% | 9,718 | 1.0 | 112.8 |
| **Need Attention** | 482 | 9.7% | 5,364,184 | 0.5% | 11,129 | 1.1 | 74.2 |
| **New Customers** | 167 | 3.4% | 1,789,843 | 0.2% | 10,718 | 1.0 | 18.4 |
| **About to Sleep** | 188 | 3.8% | 1,772,786 | 0.2% | 9,430 | 1.0 | 51.7 |

## 🏆 Ключевые сегменты

### Champions (1,368 продавцов - 27.7%)
**Характеристики:**
- Высокая частота заказов (138.4 в среднем)
- Недавняя активность (12.2 дня)
- Высокая средняя выручка (337,094 руб.)
- **41.9% от общей выручки**

**Топ-5 Champions:**
1. **ID: 32604441188337** - 107,801,432 руб., 4,118 заказов, 8 дней назад
2. **ID: 28099867** - 75,411,205 руб., 2,773 заказа, 8 дней назад  
3. **ID: 14046499** - 38,640,361 руб., 2,171 заказ, 2 дня назад
4. **ID: 203929877693052** - 37,567,105 руб., 447 заказов, 6 дней назад
5. **ID: 23755644** - 30,920,562 руб., 424 заказа, 8 дней назад

### At Risk (601 продавец - 12.2%)
**Характеристики:**
- Средняя частота (5.1 заказов)
- Давно неактивны (126.8 дней)
- **Очень высокая средняя выручка (964,035 руб.)**
- **52.7% от общей выручки** - критично!

**Проблема:** Самые доходные продавцы находятся в зоне риска!

### Lost (634 продавца - 12.8%)
**Характеристики:**
- Низкая частота (1.0 заказ)
- Давно неактивны (112.8 дней)
- Низкая выручка (9,718 руб.)
- Только 0.6% от выручки

## 🚨 Критические инсайты

### 1. Парадокс выручки:
- **At Risk сегмент** (12.2% продавцов) генерирует **52.7% выручки**
- **Champions** (27.7% продавцов) генерируют **41.9% выручки**
- **Остальные 7 сегментов** (60.1% продавцов) дают только **5.4% выручки**

### 2. Концентрация рисков:
- **94.6% выручки** зависит от **39.9% продавцов** (At Risk + Champions)
- Потеря At Risk сегмента = потеря половины бизнеса

### 3. Структура матрицы:
- **Правый нижний угол** (R5F5): самая ценная ячейка
- **Левый верхний угол** (R1F1): наибольшая группа проблемных продавцов
- **Диагональ**: четкая корреляция между recency и frequency

## 📈 Рекомендации по сегментам

### 🔥 Критический приоритет - At Risk (601 продавец):
1. **Немедленная персональная работа** с каждым продавцом
2. **Специальные предложения** для возврата активности
3. **Анализ причин** снижения активности
4. **Программа реактивации** с индивидуальным подходом
5. **Мониторинг конкурентов** - куда ушли продавцы

### 🏆 Высокий приоритет - Champions (1,368 продавцов):
1. **VIP-обслуживание** и персональные менеджеры
2. **Программы лояльности** и эксклюзивные условия
3. **Превентивные меры** против перехода в At Risk
4. **Расширение ассортимента** и категорий
5. **Партнерские программы** и совместные проекты

### 📊 Средний приоритет - Promising (712 продавцов):
1. **Программы развития** и обучения
2. **Маркетинговая поддержка** продвижения
3. **Стимулирование частоты** заказов
4. **Менторство** от Champions

### ⚠️ Низкий приоритет - Lost/Need Attention (1,116 продавцов):
1. **Автоматизированные кампании** реактивации
2. **Массовые акции** и стимулы
3. **Анализ барьеров** для возврата
4. **Сегментированный подход** по потенциалу

## 🎯 Стратегические инициативы

### 1. Система раннего предупреждения:
- Мониторинг перехода Champions → At Risk
- Автоматические алерты при снижении активности
- Predictive analytics для прогнозирования оттока

### 2. Программа удержания At Risk:
- Персональные менеджеры для топ-100 At Risk
- Специальные условия и льготы
- Анализ и устранение барьеров

### 3. Развитие Champions:
- Программы масштабирования бизнеса
- Эксклюзивные возможности и каналы
- Партнерские отношения

### 4. Оптимизация матрицы:
- Цель: увеличить плотность в правом нижнем углу
- Перевод продавцов из левых ячеек в правые
- Удержание в верхних ячейках (высокий Recency)

## 📁 Созданные файлы

- `rf_analysis_detailed.csv` - детальные RF метрики всех продавцов
- `rf_segments_analysis.csv` - анализ по сегментам
- `rf_matrix_count.csv` - матрица количества продавцов
- `rf_matrix_revenue.csv` - матрица средней выручки
- `rf_matrix_dashboard.png` - визуализации и heatmaps
- `rf_matrix_report.md` - данный отчет

---
*RF Matrix анализ выполнен на основе 212,187 заказов за период январь-май 2025 года*
