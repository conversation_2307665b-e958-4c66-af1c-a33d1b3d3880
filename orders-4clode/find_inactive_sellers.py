import pandas as pd
import numpy as np

def load_and_combine_data():
    """Загружает и объединяет все CSV файлы с данными о заказах"""
    print("📊 Загрузка данных...")
    
    # Список файлов для загрузки
    files = [
        'orders_info_ru_01-2025.csv',
        'orders_info_ru_02-2025.csv', 
        'orders_info_ru_03-2025.csv',
        'orders_info_ru_04-2025.csv',
        'orders_info_ru_05-2025.csv'
    ]
    
    dataframes = []
    for file in files:
        try:
            df = pd.read_csv(file)
            df['month'] = file.split('_')[-1].split('-')[0]  # Извлекаем месяц из имени файла
            dataframes.append(df)
            print(f"✅ Загружен {file}: {len(df)} записей")
        except Exception as e:
            print(f"❌ Ошибка загрузки {file}: {e}")
    
    # Объединяем все данные
    combined_df = pd.concat(dataframes, ignore_index=True)
    print(f"\n🎯 Общий объем данных: {len(combined_df)} записей")
    return combined_df

def find_inactive_sellers_in_may(df):
    """Находит продавцов, которые перестали продавать в мае"""
    print("\n" + "="*60)
    print("🔍 ПОИСК ПРОДАВЦОВ, КОТОРЫЕ ПЕРЕСТАЛИ ПРОДАВАТЬ В МАЕ")
    print("="*60)
    
    # Получаем уникальных продавцов по месяцам
    sellers_by_month = {}
    
    for month in ['01', '02', '03', '04', '05']:
        month_data = df[df['month'] == month]
        sellers_by_month[month] = set(month_data['seller_id'].unique())
        print(f"📊 Месяц {month}: {len(sellers_by_month[month])} уникальных продавцов")
    
    # Находим продавцов, которые были активны в предыдущие месяцы, но не в мае
    may_sellers = sellers_by_month.get('05', set())
    
    # Продавцы, которые были активны в апреле, но не в мае
    april_sellers = sellers_by_month.get('04', set())
    stopped_in_may_from_april = april_sellers - may_sellers
    
    # Продавцы, которые были активны в любом из предыдущих месяцев, но не в мае
    all_previous_sellers = set()
    for month in ['01', '02', '03', '04']:
        all_previous_sellers.update(sellers_by_month.get(month, set()))
    
    stopped_in_may_total = all_previous_sellers - may_sellers
    
    print(f"\n📈 РЕЗУЛЬТАТЫ АНАЛИЗА:")
    print(f"  Продавцов активных в мае: {len(may_sellers)}")
    print(f"  Продавцов, которые перестали продавать в мае (были в апреле): {len(stopped_in_may_from_april)}")
    print(f"  Продавцов, которые перестали продавать в мае (были в любом предыдущем месяце): {len(stopped_in_may_total)}")
    
    # Анализируем детали по продавцам, которые перестали продавать
    if len(stopped_in_may_from_april) > 0:
        print(f"\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ПРОДАВЦОВ, ПЕРЕСТАВШИХ ПРОДАВАТЬ В МАЕ:")
        
        # Получаем информацию о продавцах из предыдущих месяцев
        stopped_sellers_info = []
        
        for seller_id in list(stopped_in_may_from_april)[:50]:  # Ограничиваем до 50
            seller_data = df[df['seller_id'] == seller_id]
            
            # Последний месяц активности
            last_active_month = seller_data['month'].max()
            
            # Статистика по продавцу
            total_orders = len(seller_data)
            total_revenue = seller_data['sum'].sum()
            avg_order_value = seller_data['sum'].mean()
            seller_rating = seller_data['seller_rating'].iloc[0] if len(seller_data) > 0 else 0
            
            # Активность по месяцам
            monthly_orders = seller_data.groupby('month')['id'].count()
            monthly_revenue = seller_data.groupby('month')['sum'].sum()
            
            stopped_sellers_info.append({
                'seller_id': seller_id,
                'last_active_month': last_active_month,
                'total_orders': total_orders,
                'total_revenue': total_revenue,
                'avg_order_value': avg_order_value,
                'seller_rating': seller_rating,
                'monthly_orders': monthly_orders.to_dict(),
                'monthly_revenue': monthly_revenue.to_dict()
            })
        
        # Сортируем по общей выручке (убывание)
        stopped_sellers_info.sort(key=lambda x: x['total_revenue'], reverse=True)
        
        print(f"\n🏆 ТОП-15 ПРОДАВЦОВ ПО ВЫРУЧКЕ, КОТОРЫЕ ПЕРЕСТАЛИ ПРОДАВАТЬ В МАЕ:")
        for i, seller in enumerate(stopped_sellers_info[:15], 1):
            print(f"  {i}. ID: {seller['seller_id']}")
            print(f"     Последний активный месяц: {seller['last_active_month']}")
            print(f"     Общая выручка: {seller['total_revenue']:,.2f} руб.")
            print(f"     Заказов всего: {seller['total_orders']}")
            print(f"     Средний чек: {seller['avg_order_value']:,.2f} руб.")
            print(f"     Рейтинг: {seller['seller_rating']}")
            
            # Показываем активность по месяцам
            print(f"     Заказы по месяцам: {seller['monthly_orders']}")
            print()
        
        # Создаем DataFrame для сохранения
        stopped_sellers_df = df[df['seller_id'].isin(stopped_in_may_from_april)]
        
        return stopped_sellers_df, stopped_sellers_info
    
    else:
        print("✅ Все продавцы из апреля продолжили работать в мае!")
        return pd.DataFrame(), []

def save_inactive_sellers(df_inactive, sellers_info):
    """Сохраняет информацию о неактивных продавцах"""
    if len(df_inactive) > 0:
        # Сохраняем все заказы неактивных продавцов
        filename_orders = 'inactive_sellers_orders.csv'
        df_inactive.to_csv(filename_orders, index=False)
        print(f"\n✅ Заказы неактивных продавцов сохранены в файл: {filename_orders}")
        print(f"📊 Размер файла: {len(df_inactive)} записей")
        
        # Создаем сводную таблицу по продавцам
        summary_data = []
        for seller in sellers_info:
            summary_data.append({
                'seller_id': seller['seller_id'],
                'last_active_month': seller['last_active_month'],
                'total_orders': seller['total_orders'],
                'total_revenue': seller['total_revenue'],
                'avg_order_value': seller['avg_order_value'],
                'seller_rating': seller['seller_rating']
            })
        
        summary_df = pd.DataFrame(summary_data)
        filename_summary = 'inactive_sellers_summary.csv'
        summary_df.to_csv(filename_summary, index=False)
        print(f"✅ Сводка по неактивным продавцам сохранена в файл: {filename_summary}")
        
        return filename_orders, filename_summary
    else:
        print("❌ Нет неактивных продавцов для сохранения")
        return None, None

def main():
    """Основная функция для поиска неактивных продавцов"""
    print("🚀 ПОИСК ПРОДАВЦОВ, КОТОРЫЕ ПЕРЕСТАЛИ ПРОДАВАТЬ В МАЕ")
    print("="*60)
    
    try:
        # 1. Загрузка данных
        df = load_and_combine_data()
        
        # 2. Поиск неактивных продавцов
        inactive_orders, inactive_sellers_info = find_inactive_sellers_in_may(df)
        
        # 3. Сохранение результатов
        files = save_inactive_sellers(inactive_orders, inactive_sellers_info)
        
        print("\n🎉 АНАЛИЗ ЗАВЕРШЕН УСПЕШНО!")
        print("="*60)
        if files[0]:
            print(f"📋 Созданы файлы:")
            print(f"  - {files[0]} (все заказы неактивных продавцов)")
            print(f"  - {files[1]} (сводка по неактивным продавцам)")
        
        return df, inactive_orders, inactive_sellers_info
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении анализа: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    # Запуск анализа
    original_data, inactive_data, sellers_info = main()
